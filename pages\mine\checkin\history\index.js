// pages/mine/checkin/history/index.js
import checkinApi from '../../../../api/modules/employee-checkin.js';
import Session from '../../../../common/Session.js';

Page({
  data: {
    userInfo: {},
    checkinList: [],
    loading: false,
    hasMore: true,
    current: 1,
    pageSize: 10,
    startDate: '',
    endDate: '',
    keyword: '',
    showDatePicker: false,
    datePickerType: '', // 'start' 或 'end'
  },

  onLoad() {
    this.initUserInfo();
    this.loadCheckinList();
  },

  // 初始化用户信息
  initUserInfo() {
    const userInfo = Session.getUser();
    if (!userInfo || !userInfo.id) {
      wx.redirectTo({
        url: '/pages/login/index',
      });
      return;
    }
    this.setData({ userInfo });
  },

  // 加载打卡记录列表
  async loadCheckinList(refresh = false) {
    if (this.data.loading) return;
    
    if (refresh) {
      this.setData({
        current: 1,
        checkinList: [],
        hasMore: true
      });
    }

    if (!this.data.hasMore) return;

    this.setData({ loading: true });

    try {
      const params = {
        current: this.data.current,
        pageSize: this.data.pageSize
      };

      // 添加筛选条件
      if (this.data.startDate) {
        params.startDate = this.data.startDate;
      }
      if (this.data.endDate) {
        params.endDate = this.data.endDate;
      }
      if (this.data.keyword.trim()) {
        params.keyword = this.data.keyword.trim();
      }

      const result = await checkinApi.getList(this.data.userInfo.id, params);
      
      if (result && result.list) {
        // 格式化时间显示
        const formattedList = result.list.map(item => ({
          ...item,
          formatTime: this.formatDate(item.checkInTime)
        }));

        const newList = refresh ? formattedList : [...this.data.checkinList, ...formattedList];

        this.setData({
          checkinList: newList,
          current: this.data.current + 1,
          hasMore: result.list.length >= this.data.pageSize,
          loading: false
        });
      } else {
        this.setData({
          loading: false,
          hasMore: false
        });
      }
    } catch (error) {
      console.error('加载打卡记录失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadCheckinList(true).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom() {
    this.loadCheckinList();
  },

  // 搜索输入
  onKeywordInput(e) {
    this.setData({
      keyword: e.detail.value
    });
  },

  // 搜索
  onSearch() {
    this.loadCheckinList(true);
  },

  // 显示日期选择器
  showDatePicker(e) {
    const { type } = e.currentTarget.dataset;
    this.setData({
      showDatePicker: true,
      datePickerType: type
    });
  },

  // 日期选择确认
  onDateChange(e) {
    const { value } = e.detail;
    const { datePickerType } = this.data;
    
    if (datePickerType === 'start') {
      this.setData({ startDate: value });
    } else if (datePickerType === 'end') {
      this.setData({ endDate: value });
    }
    
    this.setData({ showDatePicker: false });
    
    // 自动搜索
    this.loadCheckinList(true);
  },

  // 取消日期选择
  onDateCancel() {
    this.setData({ showDatePicker: false });
  },

  // 清除筛选条件
  clearFilters() {
    this.setData({
      startDate: '',
      endDate: '',
      keyword: ''
    });
    this.loadCheckinList(true);
  },

  // 预览照片
  previewImage(e) {
    const { url, photos } = e.currentTarget.dataset;
    
    wx.previewImage({
      current: url,
      urls: photos
    });
  },

  // 删除打卡记录
  deleteCheckin(e) {
    const { id, index } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条打卡记录吗？删除后无法恢复。',
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await checkinApi.delete(id);

            // 检查删除结果
            if (result !== null) {
              // 从列表中移除
              const checkinList = [...this.data.checkinList];
              checkinList.splice(index, 1);
              this.setData({ checkinList });

              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            }
            // 如果 result 为 null，说明 analysisRes 已经处理了错误并显示了错误信息

          } catch (error) {
            console.error('删除失败:', error);
            wx.showToast({
              title: '网络错误，删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 格式化日期显示
  formatDate(dateStr) {
    const date = new Date(dateStr);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    const recordDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    
    if (recordDate.getTime() === today.getTime()) {
      return '今天 ' + date.toTimeString().slice(0, 5);
    } else if (recordDate.getTime() === yesterday.getTime()) {
      return '昨天 ' + date.toTimeString().slice(0, 5);
    } else {
      return date.toLocaleDateString() + ' ' + date.toTimeString().slice(0, 5);
    }
  }
});
