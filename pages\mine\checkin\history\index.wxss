.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 筛选区域 */
.filter-section {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 0;
}

.search-box {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.search-input {
  flex: 1;
  height: 80rpx;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background: #fafafa;
}

.search-btn {
  background: #5470c6;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.date-filter {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex-wrap: wrap;
}

.date-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.date-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.date-btn {
  background: #f5f5f5;
  border: none;
  border-radius: 6rpx;
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  color: #666;
}

.date-btn.selected {
  background: #5470c6;
  color: white;
}

.clear-btn {
  background: #ff6b6b;
  color: white;
  border: none;
  border-radius: 6rpx;
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

/* 列表区域 */
.list-section {
  flex: 1;
  padding: 0 30rpx;
}

.empty-state {
  text-align: center;
  padding: 120rpx 0;
  color: #999;
}

.empty-text {
  font-size: 28rpx;
}

.checkin-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 打卡卡片样式 */
.checkin-card {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: #fafbfc;
  border-bottom: 1rpx solid #f0f0f0;
}

.time-info {
  flex: 1;
}

.time-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.delete-btn {
  background: #ff6b6b;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.card-content {
  padding: 30rpx;
}

.info-row {
  display: flex;
  margin-bottom: 20rpx;
  align-items: flex-start;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  width: 120rpx;
  flex-shrink: 0;
  font-weight: 500;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  line-height: 1.5;
}

.info-value.location {
  color: #5470c6;
}

/* 照片区域 */
.photos-section {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.photos-grid {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}

.photo-item {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  border: 1rpx solid #f0f0f0;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 26rpx;
}

.no-more {
  text-align: center;
  padding: 60rpx 0;
  color: #ccc;
  font-size: 24rpx;
}
