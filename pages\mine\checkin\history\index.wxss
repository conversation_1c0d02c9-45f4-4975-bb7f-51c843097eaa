.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.header {
  background-color: #fff;
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #eee;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 筛选区域 */
.filter-section {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.search-box {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.search-input {
  flex: 1;
  height: 80rpx;
  border: 2rpx solid #eee;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.search-btn {
  background: #667eea;
  color: white;
  border: none;
  border-radius: 10rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}

.date-filter {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex-wrap: wrap;
}

.date-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.date-label {
  font-size: 26rpx;
  color: #666;
}

.date-btn {
  background: #f0f0f0;
  border: none;
  border-radius: 6rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  color: #666;
}

.date-btn.selected {
  background: #667eea;
  color: white;
}

.clear-btn {
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 6rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

/* 列表区域 */
.list-section {
  flex: 1;
}

.empty-state {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
}

.empty-icon {
  display: block;
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
}

.checkin-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 0 20rpx;
}

.checkin-item {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.checkin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.checkin-time {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.delete-btn {
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 6rpx;
  padding: 8rpx 16rpx;
  font-size: 22rpx;
}

.checkin-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.checkin-desc {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
}

.checkin-location {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.location-icon {
  font-size: 24rpx;
}

.location-text {
  font-size: 26rpx;
  color: #666;
}

.checkin-photos {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}

.checkin-photo {
  width: 150rpx;
  height: 150rpx;
  border-radius: 10rpx;
  border: 2rpx solid #f0f0f0;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 26rpx;
}

.no-more {
  text-align: center;
  padding: 40rpx 0;
  color: #ccc;
  font-size: 24rpx;
}
