import orderApi from '../../api/modules/order';
import reviewApi from '../../api/modules/review';
import { OrderStatus } from '../../common/constant';
import { formatNormalDate } from '../utils/util';
import Session from '../../common/Session';

Page({
  data: {
    userInfo: null,
    orderStatus: OrderStatus,
    // 订单类型标签（今日订单/总订单）
    orderTypeTabs: [
      { name: '今日订单', type: 'today' },
      { name: '总订单', type: 'all' },
    ],
    currentOrderType: 'today', // 当前选中的订单类型
    // 订单状态标签
    orderTabs: [
      {
        name: OrderStatus.待服务,
        status: OrderStatus.待服务,
      },
      {
        name: OrderStatus.服务中,
        status: `${OrderStatus.已出发},${OrderStatus.服务中}`,
      },
      {
        name: OrderStatus.已完成,
        status: `${OrderStatus.已完成},${OrderStatus.已评价}`,
      },
      {
        name: OrderStatus.已取消,
        status: OrderStatus.已取消,
      },
    ],
    currentTab: OrderStatus.待服务, // 当前选中的标签
    orderList: [], // 订单列表
    allOrdersCache: [], // 缓存所有订单数据用于分页
    page: 1, // 当前页码
    pageSize: 10, // 每页数量
    hasMore: true, // 是否还有更多数据
    loading: false, // 是否正在加载
    // 时间选择器相关
    showTimePicker: false, // 是否显示时间选择器
    selectedTime: '', // 选择的时间
    currentOrderId: '', // 当前操作的订单ID
    // 服务照片上传相关
    showPhotoUpload: false, // 是否显示照片上传组件
    currentPhotoType: 'before', // 当前照片类型：before（服务前）或 after（服务后）
    currentOrderInfo: null, // 当前操作的订单信息
    beforePhotos: [], // 服务前照片列表
    afterPhotos: [], // 服务后照片列表
    isServiceAction: false, // 是否是服务操作（开始服务/完成服务）
    // 追加服务相关
    pendingAdditionalServicesMap: {}, // 待确认追加服务映射表，key为订单ID
  },

  onLoad() {
    const userInfo = Session.getUser();
    this.setData({ userInfo });
    console.log('订单页面获取到的用户信息:', userInfo);

    if (!userInfo) {
      console.log('用户信息为空，跳转到登录页面');
      return wx.redirectTo({
        url: '/pages/login/index',
      });
    } else {
      // 加载初始订单数据和追加服务数据
      this.loadOrders();
      this.loadPendingAdditionalServices();
    }
  },

  onShow() {
    // 检查是否需要刷新订单列表
    const app = getApp();
    if (app.globalData.needRefreshOrderList) {
      app.globalData.needRefreshOrderList = false;
      this.resetAndReload();
    }
  },

  // 切换订单类型（今日订单/总订单）
  switchOrderType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      currentOrderType: type,
      page: 1,
      orderList: [],
      allOrdersCache: [],
      hasMore: true,
      loading: false,
    });
    this.loadOrders();
  },

  // 切换订单状态标签
  switchTab(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      currentTab: status,
      page: 1, // 重置页码
      orderList: [], // 清空当前列表
      allOrdersCache: [], // 清空缓存
      hasMore: true,
      loading: false,
    });
    this.loadOrders();
  },

  // 判断是否为今天或之前的日期
  isTodayOrBefore(dateStr) {
    if (!dateStr) return false;
    const targetDate = new Date(dateStr);
    const today = new Date();
    today.setHours(23, 59, 59, 999); // 设置为今天的最后一刻
    return targetDate <= today;
  },

  // 筛选今日订单
  filterTodayOrders(orders) {
    return orders.filter(order => this.isTodayOrBefore(order.serviceTime));
  },

  // 重置分页状态并重新加载
  resetAndReload() {
    this.setData({
      page: 1,
      orderList: [],
      allOrdersCache: [],
      hasMore: true,
      loading: false,
    });
    this.loadOrders();
    this.loadPendingAdditionalServices();
  },

  // 加载待确认的追加服务列表
  async loadPendingAdditionalServices() {
    if (!this.data.userInfo?.id) return;

    try {
      const res = await orderApi.getPendingAdditionalServices(this.data.userInfo.id);
      if (res?.list) {
        // 创建订单ID到追加服务的映射
        const pendingAdditionalServicesMap = {};
        res.list.forEach(additionalService => {
          const orderId = additionalService.orderDetail?.order?.id;
          if (orderId) {
            if (!pendingAdditionalServicesMap[orderId]) {
              pendingAdditionalServicesMap[orderId] = [];
            }
            pendingAdditionalServicesMap[orderId].push({
              ...additionalService,
              orderDetailId: additionalService.orderDetail.id,
              customerName: additionalService.customer?.name,
              customerPhone: additionalService.customer?.phone,
              orderSn: additionalService.orderDetail?.order?.sn,
            });
          }
        });

        this.setData({
          pendingAdditionalServicesMap,
        });

        // 更新订单列表中的追加服务信息
        this.updateOrderListWithAdditionalServices();
      }
    } catch (error) {
      console.error('加载待确认追加服务失败:', error);
    }
  },

  // 更新订单列表中的追加服务信息
  updateOrderListWithAdditionalServices() {
    const { orderList, pendingAdditionalServicesMap } = this.data;
    const updatedOrderList = orderList.map(order => {
      const pendingServices = pendingAdditionalServicesMap[order.id] || [];
      return {
        ...order,
        pendingAdditionalServicesCount: pendingServices.length,
        hasPendingAdditionalServices: pendingServices.length > 0,
        pendingAdditionalServices: pendingServices,
      };
    });

    this.setData({
      orderList: updatedOrderList,
    });
  },

  // 加载订单数据
  loadOrders() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    orderApi
      .myList(this.data.userInfo.id, this.data.currentTab)
      .then(res => {
        let allOrders = (res?.list || []).map(item => {
          return {
            // 保留原始数据
            ...item,
            // 添加格式化后的显示数据
            orderId: item.id,
            orderNumber: item.sn,
            status: item.status,
            statusText: item.status,
            productName: item.orderDetails?.[0].service.serviceName,
            productImage: item.orderDetails?.[0].service.logo,
            petName: item.orderDetails?.[0].petName,
            userAdress: item.addressDetail + '(' + item.addressRemark + ')',
            quantity: 1,
            expectTime: formatNormalDate(item.serviceTime),
            serviceTime: item.serviceTime, // 保留原始时间用于筛选
            createdAt: item.createdAt, // 保留原始创建时间
            extraServive: (item.orderDetails?.[0].additionalServices || []).map(v => v.name),
            originalPrice: item.originalPrice, // 原价
            totalFee: item.totalFee, // 实付金额
            // 判断是否有评价（只有已完成的订单才可能有评价，具体是否有评价通过API查询确定）
            hasReview: item.status === OrderStatus.已完成,
            // 新增的追加服务字段
            hasAdditionalServices: item.hasAdditionalServices || false,
            additionalServiceAmount: item.additionalServiceAmount || 0,
            additionalServiceOriginalPrice: item.additionalServiceOriginalPrice || 0,
            additionalServicesCompleted: item.additionalServicesCompleted || false,
            // 计算总价（保留两位小数）
            totalAmount: ((parseFloat(item.totalFee) || 0) + (parseFloat(item.additionalServiceAmount) || 0)).toFixed(2),
            // 追加服务相关（初始化为空，后续通过专门API更新）
            pendingAdditionalServicesCount: 0,
            hasPendingAdditionalServices: false,
            pendingAdditionalServices: [],
          };
        });

        // 如果是今日订单，进行日期筛选
        if (this.data.currentOrderType === 'today') {
          allOrders = this.filterTodayOrders(allOrders);
          // 今日订单显示全部，不分页
          this.setData({
            orderList: allOrders,
            hasMore: false,
            loading: false,
            allOrdersCache: allOrders, // 缓存所有订单用于分页
          });
        } else {
          // 总订单需要分页
          const { page, pageSize } = this.data;
          const startIndex = (page - 1) * pageSize;
          const endIndex = startIndex + pageSize;
          const pageData = allOrders.slice(startIndex, endIndex);

          // 判断是否还有更多数据
          const hasMore = endIndex < allOrders.length;

          this.setData({
            orderList: page === 1 ? pageData : [...this.data.orderList, ...pageData],
            hasMore,
            loading: false,
            allOrdersCache: allOrders, // 缓存所有订单用于分页
          });
        }

        // 更新追加服务信息
        this.updateOrderListWithAdditionalServices();
      })
      .catch(err => {
        console.error('加载订单失败:', err);
        this.setData({ loading: false });
      });
  },

  // 加载更多订单
  loadMoreOrders() {
    // 只有总订单才支持分页加载更多
    if (this.data.currentOrderType === 'all' && this.data.hasMore && !this.data.loading) {
      const { page, pageSize, allOrdersCache } = this.data;

      // 确保缓存数据存在
      if (!allOrdersCache || allOrdersCache.length === 0) {
        return;
      }

      const nextPage = page + 1;
      const startIndex = (nextPage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const pageData = allOrdersCache.slice(startIndex, endIndex);

      // 判断是否还有更多数据
      const hasMore = endIndex < allOrdersCache.length;

      this.setData({
        page: nextPage,
        orderList: [...this.data.orderList, ...pageData],
        hasMore,
      });
    }
  },

  // 切换更多操作弹窗
  toggleOrderActions(e) {
    const orderId = e.currentTarget.dataset.id;
    const orderList = this.data.orderList.map(order => {
      if (order.orderId === orderId) {
        order.showMoreActions = !order.showMoreActions;
      } else {
        // 关闭其他订单的更多操作
        order.showMoreActions = false;
      }
      return order;
    });

    this.setData({
      orderList,
    });
  },

  // 阻止事件冒泡
  preventTap(e) {
    e.stopPropagation();
  },

  // 查看订单详情
  viewOrderDetail(e) {
    console.log('查看订单详情:', e);
    const orderInfo = e.currentTarget.dataset.item;
    console.log('查看订单详情:', orderInfo);
    wx.setStorageSync('orderInfo', orderInfo);
    wx.navigateTo({
      url: `/pages/orders/orderDetail/index`,
    });
  },

  // 修改上门时间
  reschedule(e) {
    const orderInfo = e.currentTarget.dataset.item;
    wx.setStorageSync('orderInfo', orderInfo);
    const orderId = orderInfo.id;
    // 显示时间选择器
    this.setData({
      showTimePicker: true,
      currentOrderId: orderId,
      selectedTime: '',
    });
  },

  // 时间选择器确认
  onTimeSelected(e) {
    const selectedTime = e.detail;
    this.setData({
      selectedTime,
    });

    // 确认修改时间
    if (selectedTime) {
      this.confirmUpdateTime();
    }
  },

  // 确认修改时间
  async confirmUpdateTime() {
    if (!this.data.selectedTime) {
      wx.showToast({
        title: '请选择有效时间',
        icon: 'none',
      });
      return;
    }

    wx.showLoading({
      title: '修改中...',
    });

    try {
      const res = await orderApi.updateServiceTime(
        this.data.currentOrderId,
        this.data.userInfo.id,
        new Date(this.data.selectedTime).toISOString()
      );

      if (res) {
        wx.showToast({
          title: '修改成功',
          icon: 'success',
        });
        // 重新加载订单列表
        this.resetAndReload();
      } else {
        wx.showToast({
          title: '修改失败',
          icon: 'error',
        });
      }
    } catch (error) {
      wx.showToast({
        title: '修改失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
      // 关闭时间选择器
      this.setData({
        showTimePicker: false,
        currentOrderId: '',
        selectedTime: '',
      });
    }
  },

  // 取消时间选择
  onTimeCancel() {
    this.setData({
      showTimePicker: false,
      currentOrderId: '',
      selectedTime: '',
    });
  },

  // 出发
  dispatch(e) {
    const orderInfo = e.currentTarget.dataset.item;
    wx.setStorageSync('orderInfo', orderInfo);
    const orderId = orderInfo.id;

    // 弹窗确认
    wx.showModal({
      title: '确认出发',
      content: '确定要开始出发吗？',
      success: async res => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
          });

          try {
            const result = await orderApi.dispatch(orderId, this.data.userInfo.id);
            if (result) {
              wx.showToast({
                title: '已开始出发',
                icon: 'success',
              });
              // 刷新订单列表
              this.resetAndReload();
            } else {
              wx.showToast({
                title: '操作失败',
                icon: 'error',
              });
            }
          } catch (error) {
            wx.showToast({
              title: '操作失败',
              icon: 'error',
            });
            console.error('出发操作失败:', error);
          } finally {
            wx.hideLoading();
          }
        }
      },
    });
  },

  // 开始服务
  start(e) {
    const orderInfo = e.currentTarget.dataset.item;
    wx.setStorageSync('orderInfo', orderInfo);

    // 显示选择弹窗：直接开始服务 或 上传照片后开始服务
    wx.showActionSheet({
      itemList: ['直接开始服务', '上传服务前照片'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 直接开始服务
          this.startServiceDirectly(orderInfo);
        } else if (res.tapIndex === 1) {
          // 上传服务前照片
          this.showPhotoUploadModal(orderInfo, 'before', true);
        }
      }
    });
  },

  // 直接开始服务（不上传照片）
  async startServiceDirectly(orderInfo) {
    const orderId = orderInfo.id;

    // 弹窗确认
    wx.showModal({
      title: '确认开始服务',
      content: '确定要开始提供服务吗？',
      success: async res => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
          });

          try {
            const result = await orderApi.start(orderId, this.data.userInfo.id);
            if (result) {
              wx.showToast({
                title: '已开始服务',
                icon: 'success',
              });
              // 刷新订单列表
              this.resetAndReload();
            } else {
              wx.showToast({
                title: '操作失败',
                icon: 'error',
              });
            }
          } catch (error) {
            wx.showToast({
              title: '操作失败',
              icon: 'error',
            });
            console.error('开始服务操作失败:', error);
          } finally {
            wx.hideLoading();
          }
        }
      },
    });
  },

  // 结束服务
  complete(e) {
    const orderInfo = e.currentTarget.dataset.item;
    wx.setStorageSync('orderInfo', orderInfo);

    // 显示选择弹窗：直接完成服务 或 上传照片后完成服务
    wx.showActionSheet({
      itemList: ['直接完成服务', '上传服务后照片'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 直接完成服务
          this.completeServiceDirectly(orderInfo);
        } else if (res.tapIndex === 1) {
          // 上传服务后照片
          this.showPhotoUploadModal(orderInfo, 'after', true);
        }
      }
    });
  },

  // 直接完成服务（不上传照片）
  async completeServiceDirectly(orderInfo) {
    const orderId = orderInfo.id;

    // 弹窗确认
    wx.showModal({
      title: '确认完成服务',
      content: '确定已完成所有服务内容吗？',
      success: async res => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
          });

          try {
            const result = await orderApi.complete(orderId, this.data.userInfo.id);
            if (result) {
              wx.showToast({
                title: '服务已完成',
                icon: 'success',
              });
              // 刷新订单列表
              this.resetAndReload();
            } else {
              wx.showToast({
                title: '操作失败',
                icon: 'error',
              });
            }
          } catch (error) {
            wx.showToast({
              title: '操作失败',
              icon: 'error',
            });
            console.error('完成服务操作失败:', error);
          } finally {
            wx.hideLoading();
          }
        }
      },
    });
  },

  // 显示照片上传弹窗
  async showPhotoUploadModal(orderInfo, photoType, isServiceAction = false) {
    // 先获取已有的服务照片
    await this.loadExistingPhotos(orderInfo.id, photoType);

    this.setData({
      showPhotoUpload: true,
      currentPhotoType: photoType,
      currentOrderInfo: orderInfo,
      isServiceAction: isServiceAction, // 标记是否是服务操作（开始服务/完成服务）
    });
  },

  // 加载已有的服务照片
  async loadExistingPhotos(orderId, photoType) {
    try {
      const servicePhotos = await orderApi.getServicePhotos(orderId);
      if (servicePhotos) {
        const beforePhotos = servicePhotos.beforePhotos || [];
        const afterPhotos = servicePhotos.afterPhotos || [];

        this.setData({
          beforePhotos: photoType === 'before' ? beforePhotos : this.data.beforePhotos,
          afterPhotos: photoType === 'after' ? afterPhotos : this.data.afterPhotos,
        });
      } else {
        // 如果没有服务照片记录，清空对应的照片列表
        this.setData({
          beforePhotos: photoType === 'before' ? [] : this.data.beforePhotos,
          afterPhotos: photoType === 'after' ? [] : this.data.afterPhotos,
        });
      }
    } catch (error) {
      console.error('获取服务照片失败:', error);
      // 出错时清空对应的照片列表
      this.setData({
        beforePhotos: photoType === 'before' ? [] : this.data.beforePhotos,
        afterPhotos: photoType === 'after' ? [] : this.data.afterPhotos,
      });
    }
  },

  // 照片列表变化事件
  onPhotoChange(e) {
    const { photoList, photoType } = e.detail;
    if (photoType === 'before') {
      this.setData({ beforePhotos: photoList });
    } else {
      this.setData({ afterPhotos: photoList });
    }
  },

  // 确认上传照片并执行服务操作
  async onPhotoUploadConfirm(e) {
    const { photoList, photoType } = e.detail;
    const { currentOrderInfo, isServiceAction } = this.data;

    if (!currentOrderInfo) {
      wx.showToast({
        title: '订单信息错误',
        icon: 'none'
      });
      return;
    }

    // 关闭照片上传弹窗
    this.setData({ showPhotoUpload: false });

    const orderId = currentOrderInfo.id;
    const employeeId = this.data.userInfo.id;

    wx.showLoading({
      title: '处理中...',
    });

    try {
      let result;

      if (isServiceAction) {
        // 服务操作模式：同时执行服务操作和上传照片
        if (photoType === 'before') {
          // 开始服务并上传服务前照片
          result = await orderApi.start(orderId, employeeId, photoList);
          if (result) {
            wx.showToast({
              title: '已开始服务',
              icon: 'success',
            });
          }
        } else {
          // 完成服务并上传服务后照片
          result = await orderApi.complete(orderId, employeeId, photoList);
          if (result) {
            wx.showToast({
              title: '服务已完成',
              icon: 'success',
            });
          }
        }
      } else {
        // 单独上传照片模式：只上传照片，不执行服务操作
        // 注意：这里的photoList已经包含了已有照片和新上传的照片
        if (photoType === 'before') {
          // 单独上传服务前照片
          result = await orderApi.uploadBeforePhotos(orderId, employeeId, photoList);
          if (result) {
            wx.showToast({
              title: `服务前照片上传成功，共${photoList.length}张`,
              icon: 'success',
            });
          }
        } else {
          // 单独上传服务后照片
          result = await orderApi.uploadAfterPhotos(orderId, employeeId, photoList);
          if (result) {
            wx.showToast({
              title: `服务后照片上传成功，共${photoList.length}张`,
              icon: 'success',
            });
          }
        }
      }

      if (!result) {
        wx.showToast({
          title: '操作失败',
          icon: 'error',
        });
      }
    } catch (error) {
      wx.showToast({
        title: '操作失败',
        icon: 'error',
      });
      console.error(`${isServiceAction ? (photoType === 'before' ? '开始服务' : '完成服务') : '上传照片'}操作失败:`, error);
    } finally {
      wx.hideLoading();
      // 无论成功失败都刷新订单列表
      this.resetAndReload();
    }
  },

  // 取消照片上传
  onPhotoUploadCancel() {
    this.setData({
      showPhotoUpload: false,
      currentPhotoType: 'before',
      currentOrderInfo: null,
      beforePhotos: [],
      afterPhotos: [],
      isServiceAction: false,
    });
  },

  // 上传服务前照片（服务中状态的订单）
  uploadBeforePhotos(e) {
    const orderInfo = e.currentTarget.dataset.item;
    wx.setStorageSync('orderInfo', orderInfo);
    this.showPhotoUploadModal(orderInfo, 'before');
  },

  // 上传服务后照片（已完成状态的订单）
  uploadAfterPhotos(e) {
    const orderInfo = e.currentTarget.dataset.item;
    wx.setStorageSync('orderInfo', orderInfo);
    this.showPhotoUploadModal(orderInfo, 'after');
  },

  // 查看评价
  async viewReview(e) {
    const orderInfo = e.currentTarget.dataset.item;
    wx.setStorageSync('orderInfo', orderInfo);
    const orderId = orderInfo.id;

    wx.showLoading({
      title: '加载中...',
    });

    try {
      const reviewData = await reviewApi.getByOrderId(orderId);
      if (reviewData) {
        // 将评价信息存储到本地存储，供评价详情页使用
        wx.setStorageSync('reviewInfo', reviewData);
        wx.navigateTo({
          url: `/pages/orders/reviewDetail/index`,
        });
      } else {
        wx.showToast({
          title: '暂无评价信息',
          icon: 'none',
        });
      }
    } catch (error) {
      console.error('获取评价信息失败:', error);
      wx.showToast({
        title: '获取评价失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
    }
  },
});
