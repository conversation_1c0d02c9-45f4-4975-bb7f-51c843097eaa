<view class="container">
  <diy-navbar :isFixed="true" bgColor="white" CustomBar='60'>
    <view slot="content">
      <view class="flex align-center flex-nowrap justify-start diygw-col-24 title-left">
        打卡历史
      </view>
    </view>
  </diy-navbar>

  <!-- 筛选区域 -->
  <view class="filter-section">
    <view class="search-box">
      <input 
        class="search-input"
        placeholder="搜索打卡描述..."
        value="{{keyword}}"
        bind:input="onKeywordInput"
        bind:confirm="onSearch"
      />
      <button class="search-btn" bind:tap="onSearch">搜索</button>
    </view>
    
    <view class="date-filter">
      <view class="date-item">
        <text class="date-label">开始日期:</text>
        <button 
          class="date-btn {{startDate ? 'selected' : ''}}"
          data-type="start"
          bind:tap="showDatePicker"
        >
          {{startDate || '选择日期'}}
        </button>
      </view>
      
      <view class="date-item">
        <text class="date-label">结束日期:</text>
        <button 
          class="date-btn {{endDate ? 'selected' : ''}}"
          data-type="end"
          bind:tap="showDatePicker"
        >
          {{endDate || '选择日期'}}
        </button>
      </view>
      
      <button class="clear-btn" bind:tap="clearFilters">清除</button>
    </view>
  </view>

  <!-- 记录列表 -->
  <view class="list-section">
    <view wx:if="{{checkinList.length === 0 && !loading}}" class="empty-state">
      <text class="empty-icon">📋</text>
      <text class="empty-text">暂无打卡记录</text>
    </view>
    
    <view wx:else class="checkin-list">
      <view 
        wx:for="{{checkinList}}" 
        wx:key="id" 
        class="checkin-item"
      >
        <view class="checkin-header">
          <text class="checkin-time">{{item.checkInTime}}</text>
          <button 
            class="delete-btn"
            data-id="{{item.id}}"
            data-index="{{index}}"
            bind:tap="deleteCheckin"
          >
            删除
          </button>
        </view>
        
        <view class="checkin-content">
          <text class="checkin-desc">{{item.description}}</text>
          
          <view wx:if="{{item.address}}" class="checkin-location">
            <text class="location-icon">📍</text>
            <text class="location-text">{{item.address}}</text>
          </view>
          
          <view wx:if="{{item.photos && item.photos.length > 0}}" class="checkin-photos">
            <image 
              wx:for="{{item.photos}}" 
              wx:for-item="photo"
              wx:key="index"
              src="{{photo}}" 
              class="checkin-photo"
              mode="aspectFill"
              data-url="{{photo}}"
              data-photos="{{item.photos}}"
              bind:tap="previewImage"
            ></image>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-state">
      <text>加载中...</text>
    </view>
    
    <view wx:if="{{!hasMore && checkinList.length > 0}}" class="no-more">
      <text>没有更多记录了</text>
    </view>
  </view>

  <!-- 日期选择器 -->
  <picker
    wx:if="{{showDatePicker}}"
    mode="date"
    value="{{datePickerType === 'start' ? startDate : endDate}}"
    bind:change="onDateChange"
    bind:cancel="onDateCancel"
  >
    <view></view>
  </picker>
</view>
