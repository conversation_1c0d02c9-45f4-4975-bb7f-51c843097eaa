<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">出车拍照</text>
  </view>

  <!-- 今日统计 -->
  <view class="stats-section">
    <view class="stats-card">
      <view class="stats-item">
        <text class="stats-number">{{todayCheckins.length}}</text>
        <text class="stats-label">今日打卡</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{statistics.totalCount || 0}}</text>
        <text class="stats-label">累计打卡</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{statistics.thisMonthCount || 0}}</text>
        <text class="stats-label">本月打卡</text>
      </view>
    </view>
  </view>

  <!-- 拍照区域 -->
  <view class="photo-section">
    <view class="section-title">
      <text class="title-text">拍照打卡</text>
      <text class="title-desc">最多可上传9张照片</text>
    </view>
    
    <view class="photo-grid">
      <view 
        wx:for="{{photoList}}" 
        wx:key="index" 
        class="photo-item"
        data-url="{{item}}"
        bind:tap="previewImage"
      >
        <image src="{{item}}" class="photo-image" mode="aspectFill"></image>
        <view 
          class="photo-delete" 
          data-index="{{index}}"
          bind:tap="deletePhoto"
          catch:tap="deletePhoto"
        >
          <text class="delete-icon">×</text>
        </view>
      </view>
      
      <view 
        wx:if="{{photoList.length < 9}}" 
        class="photo-add"
        bind:tap="chooseImage"
      >
        <text class="add-icon">+</text>
        <text class="add-text">添加照片</text>
      </view>
    </view>
    
    <view wx:if="{{uploading}}" class="uploading-tip">
      <text>照片上传中...</text>
    </view>
  </view>

  <!-- 描述输入 -->
  <view class="description-section">
    <view class="section-title">
      <text class="title-text">打卡描述</text>
      <text class="title-desc">{{description.length}}/500</text>
    </view>
    <textarea 
      class="description-input"
      placeholder="请输入打卡描述（必填）"
      value="{{description}}"
      maxlength="500"
      bind:input="onDescriptionInput"
    ></textarea>
  </view>

  <!-- 位置信息 -->
  <view class="location-section">
    <view class="section-title">
      <text class="title-text">位置信息</text>
      <text class="title-desc">可选</text>
    </view>
    
    <view wx:if="{{!location}}" class="location-empty">
      <button class="location-btn" bind:tap="getLocation">
        <text class="location-icon">📍</text>
        <text>获取当前位置</text>
      </button>
    </view>
    
    <view wx:else class="location-info">
      <view class="location-text">
        <text class="location-address">{{location.address}}</text>
        <text class="location-coords">{{location.latitude}}, {{location.longitude}}</text>
      </view>
      <button class="location-clear" bind:tap="clearLocation">清除</button>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button
      class="submit-btn {{photoList.length > 0 && description.trim().length > 0 ? 'active' : ''}}"
      bind:tap="submitCheckin"
      disabled="{{photoList.length === 0 || description.trim().length === 0}}"
    >
      提交打卡
    </button>
  </view>

  <!-- 今日记录 -->
  <view wx:if="{{todayCheckins.length > 0}}" class="today-section">
    <view class="section-title">
      <text class="title-text">今日记录</text>
      <text class="title-desc">{{todayCheckins.length}}条</text>
    </view>
    
    <view class="checkin-list">
      <view 
        wx:for="{{todayCheckins}}" 
        wx:key="id" 
        class="checkin-item"
      >
        <view class="checkin-time">{{item.checkInTime}}</view>
        <view class="checkin-desc">{{item.description}}</view>
        <view wx:if="{{item.address}}" class="checkin-location">📍 {{item.address}}</view>
        <view class="checkin-photos">
          <image 
            wx:for="{{item.photos}}" 
            wx:for-item="photo"
            wx:key="index"
            src="{{photo}}" 
            class="checkin-photo"
            mode="aspectFill"
            data-url="{{photo}}"
            bind:tap="previewImage"
          ></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作按钮容器 -->
  <view class="bottom-action-container">
    <view class="action-buttons-row">
      <view class="modern-action-btn history-btn" bind:tap="viewHistory">
        <text class="btn-text">历史记录</text>
      </view>
      <view class="modern-action-btn statistics-btn" bind:tap="viewStatistics">
        <text class="btn-text">统计信息</text>
      </view>
    </view>
  </view>
</view>
