.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.loading-state {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

.content {
  padding-bottom: 40rpx;
}

/* 标签栏 */
.tab-bar {
  background: white;
  display: flex;
  margin-bottom: 20rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  border-bottom: 4rpx solid transparent;
}

.tab-item.active {
  color: #667eea;
  border-bottom-color: #667eea;
  font-weight: bold;
}

/* 总览页面 */
.overview-content {
  padding: 0 20rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stats-card {
  background: white;
  border-radius: 15rpx;
  padding: 40rpx 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.stats-card.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stats-card.secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.stats-card.tertiary {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.stats-card.quaternary {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.stats-number {
  font-size: 48rpx;
  font-weight: bold;
}

.stats-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 平均值统计 */
.average-section {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.average-grid {
  display: flex;
  justify-content: space-around;
}

.average-item {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.average-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #667eea;
}

.average-label {
  font-size: 24rpx;
  color: #666;
}

/* 最近7天 */
.recent-section {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
}

.recent-grid {
  display: flex;
  justify-content: space-between;
  gap: 10rpx;
}

.recent-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  background: #f9f9f9;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.recent-date {
  font-size: 22rpx;
  color: #666;
}

.recent-count {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

/* 月度统计 */
.monthly-content {
  padding: 0 20rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

.monthly-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.monthly-item {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.monthly-info {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.monthly-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.monthly-year {
  font-size: 24rpx;
  color: #666;
}

.monthly-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5rpx;
}

.monthly-count {
  font-size: 28rpx;
  font-weight: bold;
  color: #667eea;
}

.monthly-days {
  font-size: 22rpx;
  color: #999;
}

/* 周度统计 */
.weekly-content {
  padding: 0 20rpx;
}

.weekly-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.weekly-item {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.weekly-info {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.weekly-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.weekly-range {
  font-size: 24rpx;
  color: #666;
}

.weekly-stats {
  display: flex;
  align-items: center;
}

.weekly-count {
  font-size: 28rpx;
  font-weight: bold;
  color: #667eea;
}

/* 每日统计 */
.daily-content {
  padding: 0 20rpx;
}

.daily-chart {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
}

.chart-header {
  margin-bottom: 30rpx;
}

.chart-header text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.chart-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.chart-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.chart-label {
  width: 80rpx;
  font-size: 24rpx;
  color: #666;
}

.chart-bar {
  flex: 1;
  height: 20rpx;
  background: #f0f0f0;
  border-radius: 10rpx;
  overflow: hidden;
}

.chart-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 10rpx;
}

.chart-value {
  width: 80rpx;
  text-align: right;
  font-size: 24rpx;
  color: #666;
}
