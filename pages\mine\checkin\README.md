# 员工出车拍照功能

## 功能概述

员工出车拍照功能是一个专门为员工提供的打卡系统，允许员工在出车时拍照记录工作状态，管理端可以实时查看员工的打卡情况。

## 功能特性

### 1. 员工端功能
- **拍照打卡**：支持上传1-9张照片
- **位置记录**：可选择记录打卡位置（经纬度和地址）
- **描述信息**：可添加打卡描述（最多500字符）
- **历史记录**：查看个人打卡历史
- **今日记录**：快速查看今日打卡情况
- **统计信息**：查看个人打卡统计

### 2. 主要页面
- **主页面** (`pages/mine/checkin/index`): 拍照打卡、今日记录、统计概览
- **历史记录** (`pages/mine/checkin/history/index`): 打卡历史列表、筛选查询
- **统计页面** (`pages/mine/checkin/statistics/index`): 详细统计分析

## 文件结构

```
pages/mine/checkin/
├── index.js              # 主页面逻辑
├── index.json            # 主页面配置
├── index.wxml            # 主页面模板
├── index.wxss            # 主页面样式
├── history/
│   ├── index.js          # 历史记录页面逻辑
│   ├── index.json        # 历史记录页面配置
│   ├── index.wxml        # 历史记录页面模板
│   └── index.wxss        # 历史记录页面样式
├── statistics/
│   ├── index.js          # 统计页面逻辑
│   ├── index.json        # 统计页面配置
│   ├── index.wxml        # 统计页面模板
│   └── index.wxss        # 统计页面样式
└── README.md             # 功能说明文档
```

## API接口

### 1. 创建打卡记录
- **接口**: `POST /openapi/employee-checkins`
- **功能**: 创建新的打卡记录
- **参数**: 
  - `employeeId`: 员工ID
  - `photos`: 照片数组（1-9张）
  - `description`: 打卡描述
  - `latitude`: 纬度（可选）
  - `longitude`: 经度（可选）
  - `address`: 地址（可选）

### 2. 获取打卡记录列表
- **接口**: `GET /openapi/employee-checkins/employee/{employeeId}`
- **功能**: 获取员工的打卡记录列表，支持分页和筛选
- **参数**: 
  - `current`: 当前页码
  - `pageSize`: 每页数量
  - `startDate`: 开始日期（可选）
  - `endDate`: 结束日期（可选）
  - `keyword`: 关键词搜索（可选）

### 3. 获取今日打卡记录
- **接口**: `GET /openapi/employee-checkins/employee/{employeeId}/today`
- **功能**: 获取员工今日的打卡记录

### 4. 获取打卡统计信息
- **接口**: `GET /openapi/employee-checkins/employee/{employeeId}/statistics`
- **功能**: 获取员工的打卡统计数据

### 5. 删除打卡记录
- **接口**: `POST /openapi/employee-checkins/{id}`
- **功能**: 删除指定的打卡记录

## 主要功能

### 拍照打卡
1. 支持选择1-9张照片
2. 照片自动上传到云存储
3. 必须填写打卡描述
4. 可选择记录位置信息

### 位置记录
1. 自动获取GPS位置
2. 支持位置权限管理
3. 显示经纬度坐标
4. 可选择是否记录位置

### 历史记录
1. 分页加载打卡记录
2. 支持日期范围筛选
3. 支持关键词搜索
4. 支持删除记录

### 统计分析
1. 今日/本周/本月/累计统计
2. 平均打卡频率分析
3. 最近7天趋势
4. 月度/周度统计

## 使用说明

### 1. 入口访问
- 在"我的"页面点击"出车拍照"按钮
- 需要员工登录状态

### 2. 拍照打卡流程
1. 点击"添加照片"选择或拍摄照片
2. 填写打卡描述（必填）
3. 可选择获取当前位置
4. 点击"提交打卡"完成

### 3. 查看记录
- 主页面显示今日记录
- 点击"历史记录"查看所有记录
- 点击"统计信息"查看详细统计

### 4. 权限要求
- 相机权限：用于拍照
- 相册权限：用于选择照片
- 位置权限：用于记录位置（可选）

## 技术实现

### 1. 照片上传
- 使用小程序的`wx.chooseMedia`选择照片
- 通过云存储上传接口上传照片
- 支持多张照片批量上传

### 2. 位置获取
- 使用`wx.getLocation`获取GPS位置
- 支持权限检查和授权流程
- 错误处理和用户引导

### 3. 数据管理
- 本地缓存用户信息
- 分页加载优化性能
- 实时刷新数据

### 4. 用户体验
- 响应式设计适配不同屏幕
- 加载状态提示
- 错误处理和用户反馈

## 注意事项

1. **照片限制**：每次打卡最少1张，最多9张照片
2. **描述必填**：打卡描述为必填项，最多500字符
3. **位置可选**：位置信息为可选项，用户可选择是否记录
4. **权限管理**：需要合理处理相机、相册、位置权限
5. **网络依赖**：功能依赖网络连接，需要考虑离线场景
6. **数据安全**：照片和位置信息需要安全处理

## 扩展功能

### 可能的扩展方向
1. **推送通知**：员工打卡时向管理端推送通知
2. **地理围栏**：限制打卡位置范围
3. **人脸识别**：结合人脸识别验证员工身份
4. **工作轨迹**：基于打卡记录生成员工工作轨迹
5. **考勤统计**：结合打卡数据生成考勤报表
6. **异常检测**：检测异常打卡行为

## 更新日志

### v1.4.0 (2024-06-22)
- 修复API返回结果处理逻辑，正确判断操作成功/失败
- 完善错误处理机制，区分业务错误和网络错误
- 优化用户反馈，只在真正成功时显示成功提示
- 统一所有API调用的错误处理方式

### v1.3.0 (2024-06-22)
- 修复位置获取功能，集成腾讯地图逆地理编码API
- 修复提交按钮状态逻辑，确保按钮能正确激活
- 优化按钮样式，与系统其他页面保持一致
- 改进位置信息显示，地址更加清晰易读

### v1.2.0 (2024-06-22)
- 移除底部冗余按钮，优化界面布局
- 统计卡片添加点击事件，直接跳转统计页面
- 头部右侧保留历史记录快捷入口
- 添加统计卡片点击提示，提升用户体验

### v1.1.0 (2024-06-22)
- 统一页面头部样式，与系统其他页面保持一致
- 优化底部按钮样式，采用现代化设计
- 移除自定义导航栏，使用系统标准导航
- 调整布局层级，避免按钮重叠问题

### v1.0.0 (2024-06-22)
- 初始版本发布
- 实现基础拍照打卡功能
- 支持历史记录查看
- 支持统计分析
- 支持位置记录
