// pages/mine/checkin/index.js
import checkinApi from '../../../api/modules/employee-checkin.js';
import Session from '../../../common/Session.js';

Page({
  data: {
    userInfo: {},
    photoList: [], // 拍照列表
    description: '', // 打卡描述
    location: null, // 位置信息
    uploading: false, // 上传状态
    todayCheckins: [], // 今日打卡记录
    statistics: {}, // 统计信息
    showLocationPicker: false, // 是否显示位置选择
    canSubmit: false, // 是否可以提交
  },

  onLoad() {
    this.initUserInfo();
    this.loadTodayCheckins();
    this.loadStatistics();
  },

  onShow() {
    // 每次显示页面时刷新今日记录
    this.loadTodayCheckins();
  },

  // 初始化用户信息
  initUserInfo() {
    const userInfo = Session.getUser();
    if (!userInfo || !userInfo.id) {
      wx.redirectTo({
        url: '/pages/login/index',
      });
      return;
    }
    this.setData({ userInfo });
  },

  // 加载今日打卡记录
  async loadTodayCheckins() {
    try {
      const result = await checkinApi.getTodayList(this.data.userInfo.id);
      if (result && result.list) {
        this.setData({
          todayCheckins: result.list
        });
      }
    } catch (error) {
      console.error('加载今日打卡记录失败:', error);
    }
  },

  // 加载统计信息
  async loadStatistics() {
    try {
      const result = await checkinApi.getStatistics(this.data.userInfo.id);
      if (result) {
        this.setData({
          statistics: result
        });
      }
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  },

  // 选择照片
  chooseImage() {
    const { photoList } = this.data;
    const remainCount = 9 - photoList.length;
    
    if (remainCount <= 0) {
      wx.showToast({
        title: '最多只能上传9张照片',
        icon: 'none'
      });
      return;
    }

    this.setData({ uploading: true });

    const keyPrefix = `employee-checkin/${this.data.userInfo.id}/${Date.now()}/`;

    this.uploadImage(
      this,
      '', // 存储字段
      keyPrefix, // 上传key前缀
      remainCount // 最大数量
    ).then(res => {
      // 将新上传的照片与已有照片合并
      const newPhotoList = [...photoList, ...res];

      this.setData({
        photoList: newPhotoList,
        uploading: false
      });

      // 检查提交状态
      this.checkCanSubmit();

      wx.showToast({
        title: `上传成功，共${newPhotoList.length}张照片`,
        icon: 'success'
      });
    }).catch(error => {
      console.error('上传失败:', error);
      this.setData({ uploading: false });
      wx.showToast({
        title: '上传失败',
        icon: 'none'
      });
    });
  },

  // 删除照片
  deletePhoto(e) {
    const { index } = e.currentTarget.dataset;
    const { photoList } = this.data;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张照片吗？',
      success: (res) => {
        if (res.confirm) {
          photoList.splice(index, 1);
          this.setData({ photoList });
          // 检查提交状态
          this.checkCanSubmit();
        }
      }
    });
  },

  // 预览照片
  previewImage(e) {
    const { url } = e.currentTarget.dataset;
    const { photoList } = this.data;
    
    wx.previewImage({
      current: url,
      urls: photoList
    });
  },

  // 输入描述
  onDescriptionInput(e) {
    const description = e.detail.value;
    this.setData({
      description
    });
    this.checkCanSubmit();
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { photoList, description } = this.data;
    const canSubmit = photoList.length > 0 && description.trim().length > 0;
    this.setData({ canSubmit });
  },

  // 获取位置
  getLocation() {
    wx.showModal({
      title: '位置信息',
      content: '是否要记录当前位置信息？',
      success: (res) => {
        if (res.confirm) {
          this.getCurrentLocation();
        }
      }
    });
  },

  // 获取当前位置
  getCurrentLocation() {
    wx.showLoading({ title: '获取位置中...' });

    // 先检查位置权限
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.userLocation']) {
          // 已授权，直接获取位置
          this.getLocationData();
        } else {
          // 未授权，请求授权
          wx.authorize({
            scope: 'scope.userLocation',
            success: () => {
              this.getLocationData();
            },
            fail: () => {
              wx.hideLoading();
              wx.showModal({
                title: '位置权限',
                content: '需要获取您的位置信息来记录打卡地点，请在设置中开启位置权限',
                confirmText: '去设置',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    wx.openSetting();
                  }
                }
              });
            }
          });
        }
      }
    });
  },

  // 获取位置数据
  getLocationData() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        const { latitude, longitude } = res;

        // 使用腾讯地图逆地理编码获取地址
        this.reverseGeocode(latitude, longitude);
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('获取位置失败:', error);
        wx.showToast({
          title: '位置获取失败，请检查定位服务是否开启',
          icon: 'none'
        });
      }
    });
  },

  // 逆地理编码获取地址
  reverseGeocode(latitude, longitude) {
    // 使用腾讯地图逆地理编码API
    wx.request({
      url: 'https://apis.map.qq.com/ws/geocoder/v1/',
      data: {
        location: `${latitude},${longitude}`,
        key: 'OQRBZ-KZXKF-XEEJJ-YNVQQ-QZJQS-XQFQH', // 腾讯地图API密钥
        get_poi: 1
      },
      success: (res) => {
        wx.hideLoading();
        let address = `位置: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;

        if (res.data && res.data.status === 0 && res.data.result) {
          const result = res.data.result;
          // 优先使用推荐地址，其次使用格式化地址
          address = result.formatted_addresses?.recommend ||
                   result.address ||
                   result.formatted_addresses?.rough ||
                   address;
        }

        this.setData({
          location: {
            latitude,
            longitude,
            address
          }
        });

        wx.showToast({
          title: '位置获取成功',
          icon: 'success'
        });
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('地址解析失败:', error);

        // 地址解析失败时，仍然保存经纬度
        this.setData({
          location: {
            latitude,
            longitude,
            address: `位置: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`
          }
        });

        wx.showToast({
          title: '位置获取成功',
          icon: 'success'
        });
      }
    });
  },

  // 清除位置
  clearLocation() {
    this.setData({ location: null });
  },

  // 提交打卡
  async submitCheckin() {
    const { photoList, description, location, userInfo } = this.data;

    console.log('当前用户信息:', userInfo);
    console.log('当前token:', Session.getToken());

    // if (photoList.length === 0) {
    //   wx.showToast({
    //     title: '请至少上传一张照片',
    //     icon: 'none'
    //   });
    //   return;
    // }

    if (description.trim().length === 0) {
      wx.showToast({
        title: '请输入打卡描述',
        icon: 'none'
      });
      return;
    }

    if (description.length > 500) {
      wx.showToast({
        title: '描述不能超过500个字符',
        icon: 'none'
      });
      return;
    }

    // 确保数据格式正确，暂时使用纯英文测试
    const checkinData = {
      employeeId: parseInt(userInfo.id),
      photos: photoList || [],
      description: "Test checkin description" // 暂时使用英文测试
    };

    // 添加位置信息（如果有）
    if (location) {
      checkinData.latitude = parseFloat(location.latitude);
      checkinData.longitude = parseFloat(location.longitude);
      checkinData.address = "Test location"; // 暂时使用英文测试
    }

    console.log('提交打卡数据:', checkinData);
    console.log('数据JSON字符串:', JSON.stringify(checkinData));

    try {
      const result = await checkinApi.create(checkinData);

      // analysisRes 函数会在 errCode !== 0 时返回 null 并显示错误信息
      // 所以 result 不为 null 就表示成功
      if (result !== null) {
        wx.showToast({
          title: '打卡成功',
          icon: 'success'
        });

        // 重置表单
        this.setData({
          photoList: [],
          description: '',
          location: null,
          canSubmit: false
        });

        // 刷新今日记录和统计
        this.loadTodayCheckins();
        this.loadStatistics();
      }
      // 如果 result 为 null，说明 analysisRes 已经处理了错误并显示了错误信息

    } catch (error) {
      console.error('打卡失败:', error);
      // 处理网络错误或其他异常
      let errorMsg = '网络错误，请检查网络连接后重试';

      if (error && error.errMsg) {
        // 微信小程序的错误格式
        errorMsg = error.errMsg;
      } else if (error && error.message) {
        errorMsg = error.message;
      } else if (typeof error === 'string') {
        errorMsg = error;
      }

      wx.showToast({
        title: errorMsg,
        icon: 'none'
      });
    }
  },

  // 查看历史记录
  viewHistory() {
    wx.navigateTo({
      url: '/pages/mine/checkin/history/index'
    });
  },

  // 查看统计
  viewStatistics() {
    wx.navigateTo({
      url: '/pages/mine/checkin/statistics/index'
    });
  }
});
