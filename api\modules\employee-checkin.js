import request, { analysisRes } from "../request";
import config from "../config";

const { employeeCheckin } = config.apiUrls;

export default {
  // 创建打卡记录
  async create(data) {
    try {
      const res = await request.post(employeeCheckin.create, data, {
        loadingText: "提交中...",
      });
      const result = analysisRes(res);
      return result;
    } catch (error) {
      console.error('创建打卡记录失败:', error);
      // 重新抛出错误，让调用方处理
      throw error;
    }
  },

  // 获取员工打卡记录列表
  async getList(employeeId, params = {}) {
    try {
      const url = employeeCheckin.list.replace('{employeeId}', employeeId);
      const res = await request.get(url, {
        ...params,
        hideLoading: true,
      });
      const data = analysisRes(res);
      return data;
    } catch (error) {
      console.error('获取打卡记录列表失败:', error);
      throw error;
    }
  },

  // 获取今日打卡记录
  async getTodayList(employeeId) {
    const url = employeeCheckin.todayList.replace('{employeeId}', employeeId);
    const res = await request.get(url, {
      hideLoading: true,
    });
    const data = analysisRes(res);
    return data;
  },

  // 获取打卡统计信息
  async getStatistics(employeeId, params = {}) {
    const url = employeeCheckin.statistics.replace('{employeeId}', employeeId);
    const res = await request.get(url, {
      ...params,
      hideLoading: true,
    });
    const data = analysisRes(res);
    return data;
  },

  // 删除打卡记录
  async delete(id) {
    try {
      const url = employeeCheckin.delete.replace('{id}', id);
      const res = await request.post(url, {}, {
        loadingText: "删除中...",
      });
      const result = analysisRes(res);
      return result;
    } catch (error) {
      console.error('删除打卡记录失败:', error);
      throw error;
    }
  },
};
